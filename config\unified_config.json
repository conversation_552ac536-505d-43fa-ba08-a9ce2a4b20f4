{"_comment": "车路协同轨迹匹配系统统一配置文件", "description": "轨迹匹配系统统一配置文件", "version": "2.0", "_roi_comment": "ROI (Region of Interest) 感兴趣区域配置", "roi": {"roi_long": 20.0, "roi_lat": 5.0}, "_corridor_comment": "走廊过滤器配置 - 基于RTK轨迹的动态过滤区域", "corridor": {"enabled": true, "fallback_to_roi": true, "downsample_interval_meters": 10.0, "long_buffer_meters": 25.0, "lat_buffer_meters": 5.0, "time_buffer_seconds": 5.0, "min_trajectory_points": 10, "min_trajectory_duration": 1.0, "direction_threshold_degrees": 45.0, "point_level_filter": true}, "_matching_comment": "轨迹匹配算法核心参数配置", "matching": {"win_sec": 3.0, "local_match_thr": 0.7, "split_match_thr": 0.7, "overlap_min": 0.5, "max_gap": 2.0, "gap_match_thr": 0.6, "max_missing_gap": 5.0, "min_missing_gap": 0.5, "rtk_buffer": 2.0, "good_match_thr": 0.6, "min_segment_length": 2}, "scoring": {"method": "unified", "peak_weight": 0.6, "duration_weight": 0.3, "stability_weight": 0.1, "f1_weight": 0.8, "direction_weight": 0.2, "spatial_decay_distance": 5.0, "max_reasonable_duration": 20.0, "direction_threshold_degrees": 45.0, "min_f1_threshold": 0.3, "peak_window_duration": 5.0, "min_peak_threshold": 0.7, "segment_duration": 5.0, "quality_threshold_high": 0.8, "quality_threshold_medium": 0.6, "short_trajectory_threshold": 10.0, "sampling_rate": 10.0, "pre_filter_threshold": 0.4, "enable_score_cache": true, "cache_debug_output": false}, "anomaly": {"switch_dt": 2.0, "switch_dist": 10.0, "switch_speed": 5.0, "switch_heading": 30.0, "normal_detection_interval": 0.1, "tolerance_multiplier": 2.0, "enabled_analyzers": ["split_detection", "id_switch", "missing_gap", "trajectory_quality", "time_jump", "accuracy"]}, "accuracy_analysis": {"enabled": true, "interpolation_method": "linear", "max_time_gap": 2.0, "position_error_thresholds": [1.0, 2.0, 5.0], "speed_error_thresholds": [1.0, 3.0, 5.0], "heading_error_thresholds": [5.0, 10.0, 30.0], "statistical_metrics": ["mean", "std", "p50", "p95", "p99"], "output_detailed_data": true}, "performance": {"rtk_max_time_diff": 3.0, "enable_time_aligned_search": true, "enable_performance_logging": true}, "processing": {"time_sync_enabled": true, "spatial_filter_enabled": true, "data_validation_enabled": true, "auto_format_detection": true}, "output": {"generate_csv": true, "generate_json": true, "generate_plots": false, "include_debug_info": true}, "profiles": {"simple_distance": {"gap_match_thr": 0.6, "rtk_buffer": 2.0, "description": "使用简单距离评分的轻量级模式"}, "performance_optimized": {"performance.enable_time_aligned_search": true, "performance.rtk_max_time_diff": 2.0, "scoring.enable_score_cache": true, "scoring.pre_filter_threshold": 0.3, "description": "性能优化模式，启用时间对齐查询和评分缓存"}, "compatibility_mode": {"performance.enable_time_aligned_search": false, "scoring.enable_score_cache": false, "description": "兼容模式，使用传统算法确保最高精度"}}}